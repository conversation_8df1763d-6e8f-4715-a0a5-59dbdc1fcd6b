<?php

namespace app\controller\space;

use function think\swoole\helper\iterator;

class AssistantController extends Controller
{
    public function index()
    {
        $conversation = $this->space->conversations()
            ->with(['messages'])
            ->order('update_time desc')
            ->find();

        return [
            'conversation' => $conversation,
        ];
    }

    public function chat()
    {
        $params = $this->validate([
            'input'        => 'require',
            'conversation' => '',
            'selected'     => '',
        ]);

        $agent = $this->space->getAgent($this->getWorkspace());

        $result = $agent->run($params);

        $result->rewind();

        $generator = function () use ($result) {
            foreach ($result as $data) {
                yield 'data: ' . json_encode($data) . "\n\n";
            }
            yield "data: [DONE]\n\n";
        };

        $response = iterator($generator());

        return $response->header([
            'Content-Type'      => 'text/event-stream',
            'Cache-Control'     => 'no-cache, must-revalidate',
            'X-Accel-Buffering' => 'no',
        ]);
    }

    public function conversations($offset = null)
    {
        $query = $this->space->conversations()
            ->with(['messages'])
            ->order('id desc')
            ->limit(10);

        if ($offset) {
            $query->where('id', '<', $offset);
        }

        $conversations = $query->select();

        return json($conversations);
    }

}
