<?php

namespace app\repo\tools;

use app\repo\Workspace;
use think\agent\tool\Args;
use think\agent\tool\FunctionCall;
use think\agent\tool\result\Error;
use think\agent\tool\result\Plain;

class Download extends FunctionCall
{
    protected $title = 'Download file';

    protected $name = 'create';

    protected $description = '下载网络资源到本地。';

    protected $parameters = [
        'path' => [
            'type'        => 'string',
            'description' => '要创建的本地文件路径',
            'required'    => true,
        ],
        'url'  => [
            'type'        => 'string',
            'description' => '文件的 URL 或 base64 编码的文件数据。',
            'required'    => true,
        ],
    ];

    public function __construct(protected Workspace $workspace)
    {
    }

    protected function run(Args $args)
    {
        $path = $args->get('path');
        $url  = $args->get('url');

        // 检查文件是否已存在
        if ($this->workspace->hasFile($path)) {
            return new Error("文件已存在: {$path}");
        }

        if (str_starts_with($url, 'http')) {
            $content = fopen($url, 'r');
        } else {
            $content = base64_decode($url);
        }

        // 创建文件
        $this->workspace->writeFile($path, $content);

        return new Plain("文件创建成功: {$path}");
    }
}
