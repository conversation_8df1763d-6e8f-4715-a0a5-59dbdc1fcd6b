<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateConversationTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('conversation')
            ->addColumn(Column::integer('space_id'))
            ->addColumn(Column::string('title')->setCollation('utf8mb4_general_ci')->setNullable())
            ->addTimestamps()
            ->addSoftDelete()
            ->create();

        $this->table('message')
            ->addColumn(Column::integer('space_id'))
            ->addColumn(Column::integer('conversation_id'))
            ->addColumn(Column::text('input')->setCollation('utf8mb4_general_ci'))
            ->addColumn(Column::longText('output')->setCollation('utf8mb4_general_ci')->setNullable())
            ->addColumn(Column::integer('usage')->setDefault(0))
            ->addColumn(Column::integer('latency')->setDefault(0))
            ->addTimestamps()
            ->create();
    }
}
