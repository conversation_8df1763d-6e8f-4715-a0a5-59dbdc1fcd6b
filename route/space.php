<?php

use think\facade\Route;
use yunwuxin\auth\middleware\UseGuard;

Route::domain('*', function () {
    Route::get('/', 'index/index');
    Route::post('/', 'index/open');

    Route::redirect('preview', '/preview/');

    Route::get('preview/:path', 'preview/index')->pattern(['path' => '.*']);

    Route::get('download', 'download/index');

    Route::put('asset/:path', 'asset/upload')->pattern(['path' => '.*']);
    Route::rule('asset/:path', 'asset/download', 'GET|HEAD')->pattern(['path' => '.*']);
    Route::get('asset', 'asset/index');

    Route::get('assistant', 'assistant/index');
    Route::post('assistant/chat', 'assistant/chat');
    Route::get('assistant/conversations', 'assistant/conversations');

    Route::put('import/file', 'import/file');
    Route::post('import/:tid', 'import/save');
})->prefix('space.')->middleware(UseGuard::class, 'space');
